{"cells": [{"cell_type": "markdown", "id": "1e6b0c36-af7c-4500-9af8-6054c7f47c23", "metadata": {}, "source": ["# CSAI3121 - Machine Learning and Intelligent Data Analysis\n", "## Lab 2 - Logistic Regression\n", "#### Submission: Complete this lab exercise, answer all questions, and submit the following (zip) <span style=\"color:red\">to <PERSON><PERSON></span>:\n", "####  <span style=\"color:red\">A) The Jupyter notebook file</span>\n", "####  <span style=\"color:red\">B) The HTML or PDF of your run output</span>\n", "####  <span style=\"color:red\">C) Scanned copy of the completed GenAI declaration form with a physical signature (a handwritten signature is required)</span>\n", "#### <span style=\"color:red\">Due: 15 Sep 2025 23:59 (Monday) (late submission not accepted)</span>\n", "#### Total mark: 10 marks\n", "#### Note: For my record, please submit the hardcopy of your GenAI declaration form to me in the class.\n"]}, {"cell_type": "markdown", "id": "2764f3d5-50e3-4b5a-a74f-53f9391a0042", "metadata": {}, "source": ["\n", "### Student name: <span style=\"color:blue\">please write your name here</span>\n", "### Student ID: <span style=\"color:blue\">please write your student ID here</span>\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "7d4f067c-e666-48f7-b7fb-88bef9688e7b", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from sklearn.metrics import accuracy_score\n", "from scipy import stats\n", "from scipy.stats import t\n", "import math"]}, {"cell_type": "code", "execution_count": null, "id": "7d4f7eb9-1dfa-4b63-b020-3bcfe491e186", "metadata": {}, "outputs": [], "source": ["# Instruction:\n", "# use Pandas Dataframe.read_csv() method to load the dataset \"Default.csv\" \n", "# save the DataFrame object to a variable \"df\"."]}, {"cell_type": "code", "execution_count": null, "id": "acc7887c-a1db-48ce-ace2-6a52af35b6b3", "metadata": {}, "outputs": [], "source": ["# Student Answer:\n", "# please make up your answer according to the instructions in the last cell.\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "cbbc8040-58df-438c-b196-a194ab88065c", "metadata": {}, "outputs": [], "source": ["# Instruction:\n", "# use DataFrame.info() method to display concise information of the data \n", "# you should see four columns: \n", "# default (object), student (object), balance (float64), and income (float64)\n", "# and there are totally 10,000 entries (data points)"]}, {"cell_type": "code", "execution_count": null, "id": "34da0060-3c9e-4bd0-95af-f4a7bb05ebd0", "metadata": {}, "outputs": [], "source": ["# Student Answer:\n", "# please make up your answer according to the instructions in the last cell.\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "ecd5dcb6-c040-4773-8adb-774604738208", "metadata": {}, "outputs": [], "source": ["# Instruction:\n", "# since there are two \"object\" columns - default and student, use \n", "# DataFrame.value_counts() method to display all categories in the columns.\n", "# The meanings of these columns are as follows:\n", "# default: Yes or No -- whether client set their credit card payment as default\n", "# student: Yes or No -- whether client is a student \n", "# balance: numerical -- monthly credit card balance\n", "# income:  numerical -- annual income"]}, {"cell_type": "code", "execution_count": null, "id": "53699b3c-24a9-42e9-a2b4-57c1088b3a83", "metadata": {}, "outputs": [], "source": ["# Student Answer:\n", "# please make up your answer according to the instructions in the last cell.\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "9d99c1c8-ddc3-4d61-b727-0efeb9c09a2a", "metadata": {}, "outputs": [], "source": ["# Instruction:\n", "# use DataFrame.hist() method to display the histogram of values for\n", "# the numerical column data"]}, {"cell_type": "code", "execution_count": null, "id": "e593a644-705d-4af9-bf42-0458a13513dd", "metadata": {}, "outputs": [], "source": ["# Student Answer:\n", "# please make up your answer according to the instructions in the last cell.\n"]}, {"cell_type": "code", "execution_count": null, "id": "5cf481c7-1696-4a91-a056-fbc43c92f147", "metadata": {}, "outputs": [], "source": ["# Instruction:\n", "# Let's visualize the balance, income, and default data in a scatterplot. \n", "# For distinguishing them by different colors, we first plot data of one \n", "# class (default=Yes, in orange) and then data of another class \n", "# (default=No, in green)\n", "# \n", "# Try to understand the following codes and execute them:\n", "#\n", "# default_yes = df[df['default']=='Yes']\n", "# default_no = df[df['default']=='No']\n", "# plt.figure(figsize = (8,6))\n", "# plt.scatter(default_no['balance'], default_no['income'], alpha=0.2, label ='No Default', color='lime', edgecolor='black')\n", "# plt.scatter(default_yes['balance'], default_yes['income'], alpha=0.8, label='Default', color='orange', edgecolor='black')\n", "# plt.xlabel('Balance', fontsize=16)\n", "# plt.ylabel('Income', fontsize=16)\n", "# plt.title('Credit Card Default Data: Balance vs Income')\n", "# plt.legend()\n", "# plt.grid(True, linestyle='--', alpha=0.6)\n", "# plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "85e7940c-f5d0-4f14-8668-5054be19da83", "metadata": {}, "outputs": [], "source": ["# Student Answer:\n", "# please make up your answer according to the instructions in the last cell.\n"]}, {"cell_type": "code", "execution_count": null, "id": "7df5fb4c-1b26-439f-9e99-dbfdc5a548ed", "metadata": {}, "outputs": [], "source": ["# Instruction:\n", "# Our learning task is to predict \"default\" based on \"balance\" and/or \"income\".\n", "# However, before learning, the categorical data \"default\" must be converted into \n", "# numerical. We can do so by a simple \"map\" method, which requires an input of \n", "# the way to map.\n", "# Namely, \"Yes\" to 1, \"No\" to 0.\n", "# Remember to save the conversion result into a new column.\n", "# e.g., \n", "# df['default_num'] = df['default'].map({'Yes': 1, 'No': 0})"]}, {"cell_type": "code", "execution_count": null, "id": "b763e86c-a6a3-4114-ad38-f3a5205d04d9", "metadata": {}, "outputs": [], "source": ["# Student Answer:\n", "# please make up your answer according to the instructions in the last cell.\n"]}, {"cell_type": "code", "execution_count": null, "id": "23e2ce9a-6444-45f6-8b1d-a74c2117e20b", "metadata": {}, "outputs": [], "source": ["# Instruction:\n", "# Check if this is what you want by printing the info using DataFrame.info()\n", "#\n", "# #   Column       Non-Null Count  Dtype  \n", "#---  ------       --------------  -----  \n", "# 0   default      10000 non-null  object \n", "# 1   student      10000 non-null  object \n", "# 2   balance      10000 non-null  float64\n", "# 3   income       10000 non-null  float64#\n", "# 4   default_num  10000 non-null  int64  \n"]}, {"cell_type": "code", "execution_count": null, "id": "360081db-f865-48e6-9834-8f657728d28f", "metadata": {}, "outputs": [], "source": ["# Student Answer:\n", "# please make up your answer according to the instructions in the last cell.\n"]}, {"cell_type": "code", "execution_count": null, "id": "23a408b5-f098-43b2-be43-693d8283cccd", "metadata": {}, "outputs": [], "source": ["# Instruction:\n", "# We will do univariate analysis using the X as 'balance' and y as 'default_num'.  \n", "# Extract the feature column into X, extract the target column into y.\n", "# To extract a column in DataFrame, simply give the name of the column:\n", "# e.g., X = df[['balance']] \n", "#       this returns a 2D dataframe, suitable for X\n", "# e.g., y = df['default_num']\n", "#       this returns a 1D series, suitable for y\n", "#"]}, {"cell_type": "code", "execution_count": null, "id": "476c2752-c95a-4696-a1f7-c86d23cf96a3", "metadata": {}, "outputs": [], "source": ["# Student Answer:\n", "# please make up your answer according to the instructions in the last cell.\n"]}, {"cell_type": "code", "execution_count": null, "id": "b05cf630-570b-436f-834d-77b1c78fbeb7", "metadata": {}, "outputs": [], "source": ["# Instruction:\n", "# Like LinearRegression, scikit-learn offers a Logistic Regression classifier.\n", "# See documentation:\n", "# https://scikit-learn.org/stable/modules/generated/sklearn.linear_model.LogisticRegression.html\n", "#\n", "# Before using it, first import the library:\n", "#   from sklearn.linear_model import LogisticRegression\n", "# \n", "# Create the estimator: \n", "#   lg=LogisticRegression()\n", "#\n", "# Train the model by giving the data:\n", "#   lg.fit(X, y)"]}, {"cell_type": "code", "execution_count": null, "id": "222e770c-08aa-4432-9fb5-36e01177b81d", "metadata": {}, "outputs": [], "source": ["# Student Answer:\n", "# please make up your answer according to the instructions in the last cell.\n"]}, {"cell_type": "code", "execution_count": null, "id": "17eca4b1-b19c-43d1-b08d-53ae1af84995", "metadata": {}, "outputs": [], "source": ["# Instruction:\n", "# Now let's look at the model's estimated probabilities with balance varying from 0 to 3000 units\n", "#\n", "# create some dummy data, spanning from 0 to 3000, reshape it into 1 column format\n", "#    tmp = np.linspace(0, 3000, 300).reshape(-1,1)\n", "# \n", "# convert it into dataframe, provide a column name and save as X_new\n", "#    X_new = pd.DataFrame(tmp, columns=['balance'])\n", "#\n", "# predict 'default'\n", "#    y_new_pred = lg.predict(X_new)\n", "#\n", "# retrieve prediction probabilities\n", "#    y_new_proba = lg.predict_proba(X_new)\n", "#\n", "# note that y_new_proba has two columns, the first column is the probability of no-default (negative), \n", "# the second column is the probability of default (positive).\n", "# \n", "# display y_new_proba to see what is included there:\n", "#    y_new_proba[:5,]\n", "# \n", "# sample output:\n", "# array([[9.99976331e-01, 2.36688219e-05],\n", "#       [9.99974989e-01, 2.50113663e-05],\n", "#       [9.99973570e-01, 2.64300606e-05],\n", "#       [9.99972071e-01, 2.79292238e-05],\n", "#       [9.99970487e-01, 2.95134199e-05]])\n"]}, {"cell_type": "code", "execution_count": null, "id": "71575d20-b869-4b8c-85e0-e01b7a89d6b7", "metadata": {}, "outputs": [], "source": ["# Student Answer:\n", "# please make up your answer according to the instructions in the last cell.\n"]}, {"cell_type": "code", "execution_count": null, "id": "c8462d7a-9a5f-4c3e-af50-59034b0ebf6c", "metadata": {}, "outputs": [], "source": ["# Instruction:\n", "# We will now plot the prediction lines (as line and dashed line) and the data points as points.\n", "# For easy manipulations, extract all data points with default = 'Yes' into df_yes, \n", "# extract all data points with default = 'No' into df_no.\n", "#\n", "#    df_yes = df[df['default'] == 'Yes'] \n", "#    df_no  = df[df['default'] == 'No' ] \n", "#\n", "# Start a new figure\n", "#    plt.figure(figsize = (8,6))\n", "# \n", "# Then plot the prediction line for class \"default\" from X_new, y_proba[:,1] using the plt.plot() method; also \n", "# plot the data points from df_yes['balance'] and df_yes['default_num'] using the plt.scatter() method:\n", "#    plt.plot(X_new, y_new_proba[:,1], color=\"orange\", linestyle='-')\n", "#    plt.scatter(df_yes['balance'], df_yes['default_num'], alpha=0.2, label ='No Default', color='orange', edgecolor='black')\n", "#\n", "# also plot the prediction line and data points for class \"no-default\" in color lime.   \n", "#\n", "# Finally, add the xlabel 'Balance' and ylabel 'Probability of De<PERSON>ult', show the plot by plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "857a5700-1c2d-4727-9dbb-5c8748a4dd7f", "metadata": {}, "outputs": [], "source": ["# Student Answer:\n", "# please make up your answer according to the instructions in the last cell.\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "8102b330-f7f9-41a8-81ca-9970b45036f8", "metadata": {}, "outputs": [], "source": ["# Instruction:\n", "# We can assess the accuracy of the model's performance by computing the accuracy score.\n", "# This is provided via the sklearn.metrics library:\n", "#    from sklearn.metrics import accuracy_score, confusion_matrix\n", "#\n", "# Try to obtain the predicted values of X by calling lg.predict(X), and save the results to y_pred:\n", "#    y_pred = lg.predict(X)\n", "#\n", "# Compute the accuracy by calling accuracy_score(y, y_pred):\n", "#    accuracy_score(y, y_pred)\n", "#\n", "# Check if you obtain these outputs for accuracy_score:\n", "#   0.9725\n", "#"]}, {"cell_type": "code", "execution_count": null, "id": "e807ee92-167a-4fb0-b5ed-82f70a39cbba", "metadata": {"scrolled": true}, "outputs": [], "source": ["# Student Answer:\n", "# please make up your answer according to the instructions in the last cell.\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "d4284487-20dd-4059-a985-f24d2f4cd2f6", "metadata": {}, "outputs": [], "source": ["# Instruction:\n", "# Compute the confusion matrix by calling confusion_matrix(y, y_pred):\n", "#    confusion_matrix(y, y_pred)\n", "#\n", "# sample output:\n", "# array([[9625,   42],\n", "#       [ 233,  100]])\n", "#  Note the rows are actual & columns are predicted:\n", "#               Predict-No  Predict-Yes\n", "#   actual-No      9625       42\n", "#   actual-Yes      233      100"]}, {"cell_type": "code", "execution_count": null, "id": "0773bc79-5962-41c5-8aaf-f25015da3f9a", "metadata": {}, "outputs": [], "source": ["# Student Answer:\n", "# please make up your answer according to the instructions in the last cell.\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "cdac6427-b477-4ce2-8ed3-386f23d7f080", "metadata": {}, "outputs": [], "source": ["# Instruction:\n", "# Try to create your second model by including both \"balance\" and \"income\" variables as \n", "# features, try the model, and assess its accuracy. How does it compare to the single-variable\n", "# model?\n", "# start with:\n", "#    X2 = df[['balance','income']]\n", "#\n", "# model accuracy: 0.9737"]}, {"cell_type": "code", "execution_count": null, "id": "200580a5-e2b9-4746-9588-f59aafcf596d", "metadata": {}, "outputs": [], "source": ["# Student Answer:\n", "# please make up your answer according to the instructions in the last cell.\n"]}, {"cell_type": "code", "execution_count": null, "id": "ee1e872c-bc0a-424c-88c2-315018990ee1", "metadata": {}, "outputs": [], "source": ["# Student Answer:\n", "# please make up your answer according to the instructions in the last cell.\n"]}, {"cell_type": "code", "execution_count": null, "id": "203b873f-6e37-46d3-bae3-fa5c4413147e", "metadata": {}, "outputs": [], "source": ["# Instruction:\n", "# To evaluate the significance of the features, one can use the Logit() method from\n", "# the statsmodels library. Try to understand the following and execute:\n", "#\n", "#   import statsmodels.api as sm\n", "#   X21 = sm.add_constant(X2)\n", "#   model = sm.Logit(y, X21)\n", "#   fitted = model.fit()\n", "#   fitted.summary()\n", "#\n", "# Check the p-values (see P>|z|) to confirm if all features are significantly different \n", "# from zero. "]}, {"cell_type": "code", "execution_count": null, "id": "85346359-a38e-44cb-8866-a6e409e56dcf", "metadata": {}, "outputs": [], "source": ["# Student Answer:\n", "# please make up your answer according to the instructions in the last cell.\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "92684025-ab47-4efb-8de8-b4e0dc1a10a6", "metadata": {}, "outputs": [], "source": ["# Instruction:\n", "# Now, compute the performance of the model by calculatig the confusion matrix \n", "# and the accuracy score:\n", "# \n", "#    fitted_prob = fitted.predict(X21)\n", "#    fitted_pred = (fitted_prob >= 0.5).astype(int)\n", "#    confusion_matrix(y, fitted_pred)\n", "#    accuracy_score(y, fitted_pred)\n", "#\n", "# sample output:\n", "#   0.9737"]}, {"cell_type": "code", "execution_count": null, "id": "ead86591-1a47-4ee2-bc1f-37fd1a331cad", "metadata": {}, "outputs": [], "source": ["# Student Answer:\n", "# please make up your answer according to the instructions in the last cell.\n"]}, {"cell_type": "markdown", "id": "c2a9de6f-69cc-411d-8fb5-9f9b4db5bfae", "metadata": {}, "source": ["### <span style=\"color:red\">Do-It-Yourself Question:</span>  \n", "Train a logistic regression model with 3 variables \"balance\", \"income\", and \"student\",  \n", "to predict the target \"default\", suggest using the library \"sm\".\n", "Note that the variable \"student\" needs to be converted to numeric number to train correctly!\n", "After fitting your model, E=examine the model coefficient estimates and determine if \n", "all three variables are correlated with the target \"default\". Support your conclusion \n", "with statistical test results. \n", "\n", "#### <span style=\"color:red\">Marking:</span>\n", "* Coding [6 marks]\n", "* Discussion [4 marks]\n", "\n", "#### <span style=\"color:red\">Marking considerations:</span>\n", "* For coding: complete & correct implementation, run without errors, model summary is output\n", "* For discussion: summary of key results, critical interpretation of model's findings and implications"]}, {"cell_type": "code", "execution_count": null, "id": "0b0af6e6-9852-474c-834e-0bc39079cec1", "metadata": {}, "outputs": [], "source": ["# Student Answer: [Coding - 6 marks]\n", "# please make up your answer according to the instructions in the last cell.\n"]}, {"cell_type": "code", "execution_count": null, "id": "35ccc6c9-1ad2-4a23-84c3-7c10c4d20980", "metadata": {}, "outputs": [], "source": ["# Student Answer: [Discussion - 4 marks]\n", "# please provide your answer about significance of the coefficient estimates.\n", "\n", "\n"]}], "metadata": {"kernelspec": {"display_name": "Python [conda env:base] *", "language": "python", "name": "conda-base-py"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}